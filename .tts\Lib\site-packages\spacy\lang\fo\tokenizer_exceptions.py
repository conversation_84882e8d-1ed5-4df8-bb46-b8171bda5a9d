from ...symbols import ORTH
from ...util import update_exc
from ..tokenizer_exceptions import BASE_EXCEPTIONS

_exc = {}

for orth in [
    "apr.",
    "aug.",
    "avgr.",
    "árg.",
    "ávís.",
    "beinl.",
    "blkv.",
    "blaðkv.",
    "blm.",
    "blaðm.",
    "bls.",
    "blstj.",
    "blaðstj.",
    "des.",
    "eint.",
    "febr.",
    "fyrrv.",
    "góðk.",
    "h.m.",
    "innt.",
    "jan.",
    "kl.",
    "m.a.",
    "mðr.",
    "mió.",
    "nr.",
    "nto.",
    "nov.",
    "nút.",
    "o.a.",
    "o.a.m.",
    "o.a.tíl.",
    "o.fl.",
    "ff.",
    "o.m.a.",
    "o.o.",
    "o.s.fr.",
    "o.tíl.",
    "o.ø.",
    "okt.",
    "omf.",
    "pst.",
    "ritstj.",
    "sbr.",
    "sms.",
    "smst.",
    "smb.",
    "sb.",
    "sbrt.",
    "sp.",
    "sept.",
    "spf.",
    "spsk.",
    "t.e.",
    "t.s.",
    "t.s.s.",
    "tlf.",
    "tel.",
    "tsk.",
    "t.o.v.",
    "t.d.",
    "uml.",
    "ums.",
    "uppl.",
    "upprfr.",
    "uppr.",
    "útg.",
    "útl.",
    "útr.",
    "vanl.",
    "v.",
    "v.h.",
    "v.ø.o.",
    "viðm.",
    "viðv.",
    "vm.",
    "v.m.",
]:
    _exc[orth] = [{ORTH: orth}]
    capitalized = orth.capitalize()
    _exc[capitalized] = [{ORTH: capitalized}]

TOKENIZER_EXCEPTIONS = update_exc(BASE_EXCEPTIONS, _exc)
