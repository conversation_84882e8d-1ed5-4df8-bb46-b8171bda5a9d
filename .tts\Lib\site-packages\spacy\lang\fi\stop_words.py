# Source https://github.com/stopwords-iso/stopwords-fi/blob/master/stopwords-fi.txt
# Reformatted with some minor corrections
STOP_WORDS = set(
    """
aiemmin aika aikaa aikaan aikaisemmin aikaisin aikana aikoina aikoo aikovat
aina ainakaan ainakin ainoa ainoat aiomme aion aiotte aivan ajan alas alemmas
alkuisin alkuun alla alle aloitamme aloitan aloitat aloitatte aloitattivat
aloitettava aloitettavaksi aloitettu aloitimme aloitin aloitit aloititte
aloittaa aloittamatta aloitti aloittivat alta aluksi alussa alusta annettavaksi
annettava annettu ansiosta antaa antamatta antoi apu asia asiaa asian asiasta
asiat asioiden asioihin asioita asti avuksi avulla avun avutta

edelle edelleen edellä edeltä edemmäs edes edessä edestä ehkä ei eikä eilen
eivät eli ellei elleivät ellemme ellen ellet ellette emme en enemmän eniten
ennen ensi ensimmäinen ensimmäiseksi ensimmäisen ensimmäisenä ensimmäiset
ensimmäisiksi ensimmäisinä ensimmäisiä ensimmäistä ensin entinen entisen
entisiä entisten entistä enää eri erittäin erityisesti eräiden eräs eräät esi
esiin esillä esimerkiksi et eteen etenkin ette ettei että

halua haluaa haluamatta haluamme haluan haluat haluatte haluavat halunnut
halusi halusimme halusin halusit halusitte halusivat halutessa haluton he hei
heidän heidät heihin heille heillä heiltä heissä heistä heitä helposti heti
hetkellä hieman hitaasti huolimatta huomenna hyvien hyviin hyviksi hyville
hyviltä hyvin hyvinä hyvissä hyvistä hyviä hyvä hyvät hyvää hän häneen hänelle
hänellä häneltä hänen hänessä hänestä hänet häntä

ihan ilman ilmeisesti itse itsensä itseään

ja jo johon joiden joihin joiksi joilla joille joilta joina joissa joista joita
joka jokainen jokin joko joksi joku jolla jolle jolloin jolta jompikumpi jona
jonka jonkin jonne joo jopa jos joskus jossa josta jota jotain joten jotenkin
jotenkuten jotka jotta jouduimme jouduin jouduit jouduitte joudumme joudun
joudutte joukkoon joukossa joukosta joutua joutui joutuivat joutumaan joutuu
joutuvat juuri jälkeen jälleen jää

kahdeksan kahdeksannen kahdella kahdelle kahdelta kahden kahdessa kahdesta
kahta kahteen kai kaiken kaikille kaikilta kaikkea kaikki kaikkia kaikkiaan
kaikkialla kaikkialle kaikkialta kaikkien kaikkiin kaksi kannalta kannattaa
kanssa kanssaan kanssamme kanssani kanssanne kanssasi kauan kauemmas kaukana
kautta kehen keiden keihin keiksi keille keillä keiltä keinä keissä keistä
keitten keittä keitä keneen keneksi kenelle kenellä keneltä kenen kenenä
kenessä kenestä kenet kenettä kenties kerran kerta kertaa keskellä kesken
keskimäärin ketkä ketä kiitos kohti koko kokonaan kolmas kolme kolmen kolmesti
koska koskaan kovin kuin kuinka kuinkaan kuitenkaan kuitenkin kuka kukaan kukin
kumpainen kumpainenkaan kumpi kumpikaan kumpikin kun kuten kuuden kuusi kuutta
kylliksi kyllä kymmenen kyse

liian liki lisäksi lisää lla luo luona lähekkäin lähelle lähellä läheltä
lähemmäs lähes lähinnä lähtien läpi

mahdollisimman mahdollista me meidän meidät meihin meille meillä meiltä meissä
meistä meitä melkein melko menee menemme menen menet menette menevät meni
menimme menin menit menivät mennessä mennyt menossa mihin miksi mikä mikäli
mikään mille milloin milloinkan millä miltä minkä minne minua minulla minulle
minulta minun minussa minusta minut minuun minä missä mistä miten mitkä mitä
mitään moi molemmat mones monesti monet moni moniaalla moniaalle moniaalta
monta muassa muiden muita muka mukaan mukaansa mukana mutta muu muualla muualle
muualta muuanne muulloin muun muut muuta muutama muutaman muuten myöhemmin myös
myöskin myöskään myötä

ne neljä neljän neljää niiden niihin niiksi niille niillä niiltä niin niinä
niissä niistä niitä noiden noihin noiksi noilla noille noilta noin noina noissa
noista noita nopeammin nopeasti nopeiten nro nuo nyt näiden näihin näiksi
näille näillä näiltä näin näinä näissä näistä näitä nämä

ohi oikea oikealla oikein ole olemme olen olet olette oleva olevan olevat oli
olimme olin olisi olisimme olisin olisit olisitte olisivat olit olitte olivat
olla olleet ollut oma omaa omaan omaksi omalle omalta oman omassa omat omia
omien omiin omiksi omille omilta omissa omista on onkin onko ovat

paikoittain paitsi pakosti paljon paremmin parempi parhaillaan parhaiten
perusteella peräti pian pieneen pieneksi pienelle pienellä pieneltä pienempi
pienestä pieni pienin poikki puolesta puolestaan päälle

runsaasti

saakka sama samaa samaan samalla saman samat samoin satojen se
seitsemän sekä sen seuraavat siellä sieltä siihen siinä siis siitä sijaan siksi
sille silloin sillä silti siltä sinne sinua sinulla sinulle sinulta sinun
sinussa sinusta sinut sinuun sinä sisäkkäin sisällä siten sitten sitä ssa sta
suoraan suuntaan suuren suuret suuri suuria suurin suurten

taa taas taemmas tahansa tai takaa takaisin takana takia tallä tapauksessa
tarpeeksi tavalla tavoitteena te teidän teidät teihin teille teillä teiltä
teissä teistä teitä tietysti todella toinen toisaalla toisaalle toisaalta
toiseen toiseksi toisella toiselle toiselta toisemme toisen toisensa toisessa
toisesta toista toistaiseksi toki tosin tule tulee tulemme tulen
tulet tulette tulevat tulimme tulin tulisi tulisimme tulisin tulisit tulisitte
tulisivat tulit tulitte tulivat tulla tulleet tullut tuntuu tuo tuohon tuoksi
tuolla tuolle tuolloin tuolta tuon tuona tuonne tuossa tuosta tuota tuskin tykö
tähän täksi tälle tällä tällöin tältä tämä tämän tänne tänä tänään tässä tästä
täten tätä täysin täytyvät täytyy täällä täältä

ulkopuolella usea useasti useimmiten usein useita uudeksi uudelleen uuden uudet
uusi uusia uusien uusinta uuteen uutta

vaan vai vaiheessa vaikea vaikean vaikeat vaikeilla vaikeille vaikeilta
vaikeissa vaikeista vaikka vain varmasti varsin varsinkin varten vasen
vasemmalla vasta vastaan vastakkain vastan verran vielä vierekkäin vieressä
vieri viiden viime viimeinen viimeisen viimeksi viisi voi voidaan voimme voin
voisi voit voitte voivat vuoden vuoksi vuosi vuosien vuosina vuotta vähemmän
vähintään vähiten vähän välillä

yhdeksän yhden yhdessä yhteen yhteensä yhteydessä yhteyteen yhtä yhtäälle
yhtäällä yhtäältä yhtään yhä yksi yksin yksittäin yleensä ylemmäs yli ylös
ympäri

älköön älä
""".split()
)
