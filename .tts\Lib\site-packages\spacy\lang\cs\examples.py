"""
Example sentences to test spaCy and its language models.
>>> from spacy.lang.cs.examples import sentences
>>> docs = nlp.pipe(sentences)
"""


sentences = [
    "Máma mele maso.",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> žluťoučký kůň úpěl ďábelské ódy.",
    "ArcGIS je geografický informační systém určený pro práci s prostorovými daty.",
    "Může data vytvářet a spravovat, ale především je dokáže analyzovat, najít v nich nové vztahy a vše přehledně vizualizovat.",
    "Dnes je krásné počasí.",
    "Nestihl autobus, protože pozdě vstal z postele.",
    "<PERSON><PERSON><PERSON>ude<PERSON> j<PERSON>, jdi si umýt ruce.",
    "Dnes je neděle.",
    "Škola začíná v 8:00.",
    "Poslední autobus jede v jedenáct hodin večer.",
    "V roce 2020 se téměř zastavila světová ekonomika.",
    "<PERSON>raha je hlavní město České republiky.",
    "<PERSON><PERSON> půj<PERSON><PERSON> ven?",
    "Kam pojedete na dovolenou?",
    "Kolik stojí iPhone 12?",
    "Průměrná mzda je 30000 Kč.",
    "1. ledna 1993 byla založena Česká republika.",
    "Co se stalo 21.8.1968?",
    "Moje telefonní číslo je 712 345 678.",
    "Můj pes má blechy.",
    "Když bude přes noc více než 20°, tak nás čeká tropická noc.",
    "Kolik bylo letos tropických nocí?",
    "Jak to mám udělat?",
    "Bydlíme ve čtvrtém patře.",
    "Vysílají 30. sezonu seriálu Simpsonovi.",
    "Adresa ČVUT je Thákurova 7, 166 29, Praha 6.",
    "Jaké PSČ má Praha 1?",
    "PSČ Prahy 1 je 110 00.",
    "Za 20 minut jede vlak.",
]
