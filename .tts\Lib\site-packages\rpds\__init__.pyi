from typing import (
    ItemsView,
    Iterable,
    Iterator,
    KeysView,
    Mapping,
    TypeVar,
    ValuesView,
)

_T = TypeVar("_T")
_KT_co = TypeVar("_KT_co", covariant=True)
_VT_co = TypeVar("_VT_co", covariant=True)
_KU_co = TypeVar("_KU_co", covariant=True)
_VU_co = TypeVar("_VU_co", covariant=True)

class HashTrieMap(Mapping[_KT_co, _VT_co]):
    def __init__(
        self,
        value: Mapping[_KT_co, _VT_co] | Iterable[tuple[_KT_co, _VT_co]] = {},
        **kwds: Mapping[_KT_co, _VT_co],
    ): ...
    def __getitem__(self, key: _KT_co) -> _VT_co: ...
    def __iter__(self) -> Iterator[_KT_co]: ...
    def __len__(self) -> int: ...
    def discard(self, key: _KT_co) -> HashTrieMap[_KT_co, _VT_co]: ...
    def items(self) -> ItemsView[_KT_co, _VT_co]: ...
    def keys(self) -> KeysView[_KT_co]: ...
    def values(self) -> ValuesView[_VT_co]: ...
    def remove(self, key: _KT_co) -> HashTrieMap[_KT_co, _VT_co]: ...
    def insert(
        self,
        key: _KT_co,
        val: _VT_co,
    ) -> HashTrieMap[_KT_co, _VT_co]: ...
    def update(
        self,
        *args: Mapping[_KU_co, _VU_co] | Iterable[tuple[_KU_co, _VU_co]],
    ) -> HashTrieMap[_KT_co | _KU_co, _VT_co | _VU_co]: ...
    @classmethod
    def convert(
        cls,
        value: Mapping[_KT_co, _VT_co] | Iterable[tuple[_KT_co, _VT_co]],
    ) -> HashTrieMap[_KT_co, _VT_co]: ...
    @classmethod
    def fromkeys(
        cls,
        keys: Iterable[_KT_co],
        value: _VT_co = None,
    ) -> HashTrieMap[_KT_co, _VT_co]: ...

class HashTrieSet(frozenset[_T]):
    def __init__(self, value: Iterable[_T] = ()): ...
    def __iter__(self) -> Iterator[_T]: ...
    def __len__(self) -> int: ...
    def discard(self, value: _T) -> HashTrieSet[_T]: ...
    def remove(self, value: _T) -> HashTrieSet[_T]: ...
    def insert(self, value: _T) -> HashTrieSet[_T]: ...
    def update(self, *args: Iterable[_T]) -> HashTrieSet[_T]: ...

class List(Iterable[_T]):
    def __init__(self, value: Iterable[_T] = (), *more: _T): ...
    def __iter__(self) -> Iterator[_T]: ...
    def __len__(self) -> int: ...
    def push_front(self, value: _T) -> List[_T]: ...
    def drop_first(self) -> List[_T]: ...

class Queue(Iterable[_T]):
    def __init__(self, value: Iterable[_T] = (), *more: _T): ...
    def __iter__(self) -> Iterator[_T]: ...
    def __len__(self) -> int: ...
    def enqueue(self, value: _T) -> Queue[_T]: ...
    def dequeue(self, value: _T) -> Queue[_T]: ...
    @property
    def is_empty(self) -> _T: ...
    @property
    def peek(self) -> _T: ...
