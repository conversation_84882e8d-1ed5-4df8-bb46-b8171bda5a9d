@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Building Kokoro TTS API Server
echo ========================================

:: Set variables
set SCRIPT_NAME=kokoro_api
set PYTHON_FILE=%SCRIPT_NAME%.py
set BUILD_DIR=build
set DIST_DIR=dist

:: Check if Python file exists
if not exist "%PYTHON_FILE%" (
    echo Error: %PYTHON_FILE% not found!
    pause
    exit /b 1
)

:: Check if required dependencies are installed
echo Checking dependencies...
python -c "import fastapi, uvicorn, kokoro, torch, soundfile, numpy" 2>nul
if errorlevel 1 (
    echo Warning: Some required dependencies may not be installed.
    echo Please make sure you have installed all dependencies from requirements.txt
    echo.
    set /p continue="Continue anyway? (y/n): "
    if /i not "!continue!"=="y" (
        echo Build cancelled.
        pause
        exit /b 1
    )
)

:: Create build directories
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
if not exist "%DIST_DIR%" mkdir "%DIST_DIR%"

echo.
echo Select build method:
echo 1. PyInstaller (Recommended - Single executable)
echo 2. cx_Freeze (Cross-platform)
echo 3. Nuitka (Performance optimized)
echo 4. Auto-py-to-exe (GUI wrapper for PyInstaller)
echo 5. Build all methods
echo 6. Test API before building
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto pyinstaller
if "%choice%"=="2" goto cxfreeze
if "%choice%"=="3" goto nuitka
if "%choice%"=="4" goto autopytoexe
if "%choice%"=="5" goto buildall
if "%choice%"=="6" goto testapi
goto invalid

:pyinstaller
echo.
echo Building with PyInstaller...
echo ========================================

:: Check if PyInstaller is installed
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo PyInstaller not found. Installing...
    pip install pyinstaller
)

:: Check for required files
echo Checking for required files...
if not exist "KokoroTTS.py" (
    echo Error: KokoroTTS.py not found!
    pause
    exit /b 1
)
if not exist "en.txt" (
    echo Error: en.txt not found!
    pause
    exit /b 1
)

:: Get current directory path
set CURRENT_DIR=%CD%

:: Build with PyInstaller including Kokoro TTS dependencies
echo Building single executable with Kokoro TTS...
pyinstaller --onefile ^
    --name=%SCRIPT_NAME% ^
    --distpath=%DIST_DIR% ^
    --workpath=%BUILD_DIR% ^
    --specpath=%BUILD_DIR% ^
    --add-data="%CURRENT_DIR%\KokoroTTS.py;." ^
    --add-data="%CURRENT_DIR%\en.txt;." ^
    --hidden-import=fastapi ^
    --hidden-import=uvicorn ^
    --hidden-import=uvicorn.lifespan.on ^
    --hidden-import=uvicorn.lifespan.off ^
    --hidden-import=uvicorn.protocols.websockets.auto ^
    --hidden-import=uvicorn.protocols.http.auto ^
    --hidden-import=uvicorn.protocols.websockets.websockets_impl ^
    --hidden-import=uvicorn.protocols.http.h11_impl ^
    --hidden-import=uvicorn.protocols.http.httptools_impl ^
    --hidden-import=uvicorn.loops.auto ^
    --hidden-import=uvicorn.loops.asyncio ^
    --hidden-import=uvicorn.loops.uvloop ^
    --hidden-import=kokoro ^
    --hidden-import=torch ^
    --hidden-import=torchaudio ^
    --hidden-import=soundfile ^
    --hidden-import=numpy ^
    --hidden-import=pydantic ^
    --hidden-import=starlette ^
    --collect-data=kokoro ^
    --collect-submodules=kokoro ^
    --collect-data=torch ^
    --collect-data=transformers ^
    --collect-data=huggingface_hub ^
    %PYTHON_FILE%

if errorlevel 0 (
    echo.
    echo ✓ PyInstaller build completed successfully!
    echo Executable: %DIST_DIR%\%SCRIPT_NAME%.exe
) else (
    echo ✗ PyInstaller build failed!
)
goto end

:cxfreeze
echo.
echo Building with cx_Freeze...
echo ========================================

:: Check if cx_Freeze is installed
python -c "import cx_Freeze" 2>nul
if errorlevel 1 (
    echo cx_Freeze not found. Installing...
    pip install cx_Freeze
)

:: Create setup.py for cx_Freeze
echo Creating setup.py for cx_Freeze...
(
echo import sys
echo from cx_Freeze import setup, Executable
echo.
echo build_exe_options = {
echo     "packages": ["asyncio", "fastapi", "uvicorn", "kokoro", "torch", "torchaudio", "soundfile", "numpy", "pydantic", "starlette", "transformers", "huggingface_hub"],
echo     "excludes": [],
echo     "include_files": [("KokoroTTS.py", "KokoroTTS.py"), ("en.txt", "en.txt")]
echo }
echo.
echo base = None
echo if sys.platform == "win32":
echo     base = "Console"
echo.
echo setup(
echo     name="%SCRIPT_NAME%",
echo     version="1.0",
echo     description="Kokoro TTS API Server",
echo     options={"build_exe": build_exe_options},
echo     executables=[Executable("%PYTHON_FILE%", base=base, target_name="%SCRIPT_NAME%.exe")]
echo )
) > setup_cxfreeze.py

:: Build with cx_Freeze
python setup_cxfreeze.py build_exe --build-dir=%DIST_DIR%\cx_freeze

if errorlevel 0 (
    echo.
    echo ✓ cx_Freeze build completed successfully!
    echo Executable: %DIST_DIR%\cx_freeze\%SCRIPT_NAME%.exe
) else (
    echo ✗ cx_Freeze build failed!
)
goto end

:nuitka
echo.
echo Building with Nuitka...
echo ========================================

:: Check if Nuitka is installed
python -c "import nuitka" 2>nul
if errorlevel 1 (
    echo Nuitka not found. Installing...
    pip install nuitka
)

:: Build with Nuitka
echo Building optimized executable...
python -m nuitka --onefile ^
    --output-dir=%DIST_DIR% ^
    --output-filename=%SCRIPT_NAME%.exe ^
    --include-data-files=KokoroTTS.py=KokoroTTS.py ^
    --include-data-files=en.txt=en.txt ^
    --include-package=fastapi ^
    --include-package=uvicorn ^
    --include-package=kokoro ^
    --include-package=torch ^
    --include-package=torchaudio ^
    --include-package=soundfile ^
    --include-package=numpy ^
    --include-package=pydantic ^
    --include-package=starlette ^
    --include-package=transformers ^
    --include-package=huggingface_hub ^
    %PYTHON_FILE%

if errorlevel 0 (
    echo.
    echo ✓ Nuitka build completed successfully!
    echo Executable: %DIST_DIR%\%SCRIPT_NAME%.exe
) else (
    echo ✗ Nuitka build failed!
)
goto end

:autopytoexe
echo.
echo Launching auto-py-to-exe...
echo ========================================

:: Check if auto-py-to-exe is installed
python -c "import auto_py_to_exe" 2>nul
if errorlevel 1 (
    echo auto-py-to-exe not found. Installing...
    pip install auto-py-to-exe
)

:: Launch auto-py-to-exe GUI
echo Opening auto-py-to-exe GUI...
auto-py-to-exe
goto end

:buildall
echo.
echo Building with all methods...
echo ========================================

call :pyinstaller
echo.
call :cxfreeze
echo.
call :nuitka

echo.
echo ========================================
echo All builds completed!
echo ========================================
goto end

:testapi
echo.
echo Testing Kokoro TTS API...
echo ========================================

:: Check if test script exists
if not exist "test_kokoro_api.py" (
    echo Error: test_kokoro_api.py not found!
    echo Please make sure the test script is in the same directory.
    pause
    exit /b 1
)

:: Run the test script
echo Running API tests...
python test_kokoro_api.py

if errorlevel 1 (
    echo.
    echo ✗ Tests failed! Please fix issues before building.
    echo.
    set /p continue="Continue with build anyway? (y/n): "
    if /i not "!continue!"=="y" (
        echo Build cancelled.
        pause
        exit /b 1
    )
) else (
    echo.
    echo ✓ All tests passed! Ready to build.
    echo.
    set /p buildnow="Proceed with PyInstaller build? (y/n): "
    if /i "!buildnow!"=="y" goto pyinstaller
)
goto end

:invalid
echo Invalid choice! Please select 1-6.
pause
exit /b 1

:end
echo.
echo ========================================
echo Build process completed!
echo ========================================
echo.
echo Built files are in the '%DIST_DIR%' directory.
echo.

:: List built files
if exist "%DIST_DIR%" (
    echo Built executables:
    dir /b "%DIST_DIR%\*.exe" 2>nul
    if exist "%DIST_DIR%\cx_freeze" (
        echo.
        echo cx_Freeze build:
        dir /b "%DIST_DIR%\cx_freeze\*.exe" 2>nul
    )
)

echo.
echo ========================================
echo Usage Instructions:
echo ========================================
echo 1. Run the executable to start the API server
echo 2. The server will run on http://localhost:8000
echo 3. API endpoints:
echo    - GET  /           : Health check
echo    - POST /synthesize : Text-to-speech synthesis
echo.
echo Example API call:
echo curl -X POST "http://localhost:8000/synthesize" ^
echo      -H "Content-Type: application/json" ^
echo      -d "{\"text\":\"Hello world\",\"voice\":\"af_heart\",\"speed\":1.0}"
echo.
echo Press any key to exit...
pause >nul
