verb_roots = """
#هست
آخت#آهنج
آراست#آرا
آراماند#آرامان
آرامید#آرام
آرمید#آرام
آزرد#آزار
آزمود#آزما
آسود#آسا
آشامید#آشام
آشفت#آشوب
آشوبید#آشوب
آغازید#آغاز
آغشت#آمیز
آفرید#آفرین
آلود#آلا
آمد#آ
آمرزید#آمرز
آموخت#آموز
آموزاند#آموزان
آمیخت#آمیز
آورد#آر
آورد#آور
آویخت#آویز
آکند#آکن
آگاهانید#آگاهان
ارزید#ارز
افتاد#افت
افراخت#افراز
افراشت#افراز
افروخت#افروز
افروزید#افروز
افزود#افزا
افسرد#افسر
افشاند#افشان
افکند#افکن
افگند#افگن
انباشت#انبار
انجامید#انجام
انداخت#انداز
اندوخت#اندوز
اندود#اندا
اندیشید#اندیش
انگاشت#انگار
انگیخت#انگیز
انگیزاند#انگیزان
ایستاد#ایست
ایستاند#ایستان
باخت#باز
باراند#باران
بارگذاشت#بارگذار
بارید#بار
باز#بازخواه
بازآفرید#بازآفرین
بازآمد#بازآ
بازآموخت#بازآموز
بازآورد#بازآور
بازایستاد#بازایست
بازتابید#بازتاب
بازجست#بازجو
بازخواند#بازخوان
بازخوراند#بازخوران
بازداد#بازده
بازداشت#بازدار
بازرساند#بازرسان
بازرسانید#بازرسان
باززد#باززن
بازستاند#بازستان
بازشمارد#بازشمار
بازشمرد#بازشمار
بازشمرد#بازشمر
بازشناخت#بازشناس
بازشناساند#بازشناسان
بازفرستاد#بازفرست
بازماند#بازمان
بازنشست#بازنشین
بازنمایاند#بازنمایان
بازنهاد#بازنه
بازنگریست#بازنگر
بازپرسید#بازپرس
بازگذارد#بازگذار
بازگذاشت#بازگذار
بازگرداند#بازگردان
بازگردانید#بازگردان
بازگردید#بازگرد
بازگرفت#بازگیر
بازگشت#بازگرد
بازگشود#بازگشا
بازگفت#بازگو
بازیافت#بازیاب
بافت#باف
بالید#بال
باوراند#باوران
بایست#باید
بخشود#بخش
بخشود#بخشا
بخشید#بخش
بر#برخواه
برآشفت#برآشوب
برآمد#برآ
برآورد#برآور
برازید#براز
برافتاد#برافت
برافراخت#برافراز
برافراشت#برافراز
برافروخت#برافروز
برافشاند#برافشان
برافکند#برافکن
براند#بران
برانداخت#برانداز
برانگیخت#برانگیز
بربست#بربند
برتاباند#برتابان
برتابید#برتاب
برتافت#برتاب
برتنید#برتن
برجهید#برجه
برخاست#برخیز
برخورد#برخور
برد#بر
برداشت#بردار
بردمید#بردم
برزد#برزن
برشد#برشو
برشمارد#برشمار
برشمرد#برشمار
برشمرد#برشمر
برنشاند#برنشان
برنشانید#برنشان
برنشست#برنشین
برنهاد#برنه
برچید#برچین
برکرد#برکن
برکشید#برکش
برکند#برکن
برگذشت#برگذر
برگرداند#برگردان
برگردانید#برگردان
برگردید#برگرد
برگرفت#برگیر
برگزید#برگزین
برگشت#برگرد
برگشود#برگشا
برگمارد#برگمار
برگمارید#برگمار
برگماشت#برگمار
برید#بر
بست#بند
بلعید#بلع
بود#باش
بوسید#بوس
بویید#بو
بیخت#بیز
بیخت#بوز
تاباند#تابان
تابید#تاب
تاخت#تاز
تاراند#تاران
تازاند#تازان
تازید#تاز
تافت#تاب
ترادیسید#ترادیس
تراشاند#تراشان
تراشید#تراش
تراوید#تراو
ترساند#ترسان
ترسید#ترس
ترشاند#ترشان
ترشید#ترش
ترکاند#ترکان
ترکید#ترک
تفتید#تفت
تمرگید#تمرگ
تنید#تن
توانست#توان
توفید#توف
تپاند#تپان
تپید#تپ
تکاند#تکان
تکانید#تکان
جست#جه
جست#جو
جنباند#جنبان
جنبید#جنب
جنگید#جنگ
جهاند#جهان
جهید#جه
جوشاند#جوشان
جوشانید#جوشان
جوشید#جوش
جويد#جو
جوید#جو
خاراند#خاران
خارید#خار
خاست#خیز
خایید#خا
خراشاند#خراشان
خراشید#خراش
خرامید#خرام
خروشید#خروش
خرید#خر
خزید#خز
خسبید#خسب
خشکاند#خشکان
خشکید#خشک
خفت#خواب
خلید#خل
خماند#خمان
خمید#خم
خنداند#خندان
خندانید#خندان
خندید#خند
خواباند#خوابان
خوابانید#خوابان
خوابید#خواب
خواست#خواه
خواست#خیز
خواند#خوان
خوراند#خوران
خورد#خور
خیزاند#خیزان
خیساند#خیسان
داد#ده
داشت#دار
دانست#دان
در#درخواه
درآمد#درآ
درآمیخت#درآمیز
درآورد#درآور
درآویخت#درآویز
درافتاد#درافت
درافکند#درافکن
درانداخت#درانداز
درانید#دران
دربرد#دربر
دربرگرفت#دربرگیر
درخشاند#درخشان
درخشانید#درخشان
درخشید#درخش
درداد#درده
دررفت#دررو
درماند#درمان
درنمود#درنما
درنوردید#درنورد
درود#درو
دروید#درو
درکرد#درکن
درکشید#درکش
درگذشت#درگذر
درگرفت#درگیر
دریافت#دریاب
درید#در
دزدید#دزد
دمید#دم
دواند#دوان
دوخت#دوز
دوشید#دوش
دوید#دو
دید#بین
راند#ران
ربود#ربا
ربود#روب
رخشید#رخش
رساند#رسان
رسانید#رسان
رست#ره
رست#رو
رسید#رس
رشت#ریس
رفت#رو
رفت#روب
رقصاند#رقصان
رقصید#رقص
رماند#رمان
رمانید#رمان
رمید#رم
رنجاند#رنجان
رنجانید#رنجان
رنجید#رنج
رندید#رند
رهاند#رهان
رهانید#رهان
رهید#ره
روبید#روب
روفت#روب
رویاند#رویان
رویانید#رویان
رویید#رو
رویید#روی
ریخت#ریز
رید#رین
ریدن#رین
ریسید#ریس
زاد#زا
زارید#زار
زایاند#زایان
زایید#زا
زد#زن
زدود#زدا
زیست#زی
ساباند#سابان
سابید#ساب
ساخت#ساز
سایید#سا
ستاد#ستان
ستاند#ستان
سترد#ستر
ستود#ستا
ستیزید#ستیز
سراند#سران
سرایید#سرا
سرشت#سرش
سرود#سرا
سرکشید#سرکش
سرگرفت#سرگیر
سرید#سر
سزید#سز
سفت#سنب
سنجید#سنج
سوخت#سوز
سود#سا
سوزاند#سوزان
سپارد#سپار
سپرد#سپار
سپرد#سپر
سپوخت#سپوز
سگالید#سگال
شاشید#شاش
شایست#
شایست#شاید
شتاباند#شتابان
شتابید#شتاب
شتافت#شتاب
شد#شو
شست#شو
شست#شوی
شلید#شل
شمار#شمر
شمارد#شمار
شمرد#شمار
شمرد#شمر
شناخت#شناس
شناساند#شناسان
شنفت#شنو
شنید#شنو
شوتید#شوت
شوراند#شوران
شورید#شور
شکافت#شکاف
شکاند#شکان
شکاند#شکن
شکست#شکن
شکفت#شکف
طلبید#طلب
طپید#طپ
غراند#غران
غرید#غر
غلتاند#غلتان
غلتانید#غلتان
غلتید#غلت
غلطاند#غلطان
غلطانید#غلطان
غلطید#غلط
فرا#فراخواه
فراخواند#فراخوان
فراداشت#فرادار
فرارسید#فرارس
فرانمود#فرانما
فراگرفت#فراگیر
فرستاد#فرست
فرسود#فرسا
فرمود#فرما
فرهیخت#فرهیز
فرو#فروخواه
فروآمد#فروآ
فروآورد#فروآور
فروافتاد#فروافت
فروافکند#فروافکن
فروبرد#فروبر
فروبست#فروبند
فروخت#فروش
فروخفت#فروخواب
فروخورد#فروخور
فروداد#فروده
فرودوخت#فرودوز
فرورفت#فرورو
فروریخت#فروریز
فروشکست#فروشکن
فروفرستاد#فروفرست
فروماند#فرومان
فرونشاند#فرونشان
فرونشانید#فرونشان
فرونشست#فرونشین
فرونمود#فرونما
فرونهاد#فرونه
فروپاشاند#فروپاشان
فروپاشید#فروپاش
فروچکید#فروچک
فروکرد#فروکن
فروکشید#فروکش
فروکوبید#فروکوب
فروکوفت#فروکوب
فروگذارد#فروگذار
فروگذاشت#فروگذار
فروگرفت#فروگیر
فریفت#فریب
فشاند#فشان
فشرد#فشار
فشرد#فشر
فلسفید#فلسف
فهماند#فهمان
فهمید#فهم
قاپید#قاپ
قبولاند#قبول
قبولاند#قبولان
لاسید#لاس
لرزاند#لرزان
لرزید#لرز
لغزاند#لغزان
لغزید#لغز
لمباند#لمبان
لمید#لم
لنگید#لنگ
لولید#لول
لیسید#لیس
ماسید#ماس
مالاند#مالان
مالید#مال
ماند#مان
مانست#مان
مرد#میر
مویید#مو
مکید#مک
نازید#ناز
نالاند#نالان
نالید#نال
نامید#نام
نشاند#نشان
نشست#نشین
نمایاند#نما
نمایاند#نمایان
نمود#نما
نهاد#نه
نهفت#نهنب
نواخت#نواز
نوازید#نواز
نوردید#نورد
نوشاند#نوشان
نوشانید#نوشان
نوشت#نویس
نوشید#نوش
نکوهید#نکوه
نگاشت#نگار
نگرید#
نگریست#نگر
هراساند#هراسان
هراسانید#هراسان
هراسید#هراس
هشت#هل
وا#واخواه
واداشت#وادار
وارفت#وارو
وارهاند#وارهان
واماند#وامان
وانهاد#وانه
واکرد#واکن
واگذارد#واگذار
واگذاشت#واگذار
ور#ورخواه
ورآمد#ورآ
ورافتاد#ورافت
وررفت#وررو
ورزید#ورز
وزاند#وزان
وزید#وز
ویراست#ویرا
پاشاند#پاشان
پاشید#پاش
پالود#پالا
پایید#پا
پخت#پز
پذیراند#پذیران
پذیرفت#پذیر
پراند#پران
پراکند#پراکن
پرداخت#پرداز
پرستید#پرست
پرسید#پرس
پرهیخت#پرهیز
پرهیزید#پرهیز
پروراند#پروران
پرورد#پرور
پرید#پر
پسندید#پسند
پلاساند#پلاسان
پلاسید#پلاس
پلکید#پلک
پناهاند#پناهان
پناهید#پناه
پنداشت#پندار
پوساند#پوسان
پوسید#پوس
پوشاند#پوشان
پوشید#پوش
پویید#پو
پژمرد#پژمر
پژوهید#پژوه
پکید#پک
پیراست#پیرا
پیمود#پیما
پیوست#پیوند
پیچاند#پیچان
پیچانید#پیچان
پیچید#پیچ
چاپید#چاپ
چایید#چا
چراند#چران
چرانید#چران
چرباند#چربان
چربید#چرب
چرخاند#چرخان
چرخانید#چرخان
چرخید#چرخ
چروکید#چروک
چرید#چر
چزاند#چزان
چسباند#چسبان
چسبید#چسب
چسید#چس
چشاند#چشان
چشید#چش
چلاند#چلان
چلانید#چلان
چپاند#چپان
چپید#چپ
چکاند#چکان
چکید#چک
چید#چین
کاست#کاه
کاشت#کار
کاوید#کاو
کرد#کن
کشاند#کشان
کشانید#کشان
کشت#کار
کشت#کش
کشید#کش
کند#کن
کوباند#کوبان
کوبید#کوب
کوشید#کوش
کوفت#کوب
کوچانید#کوچان
کوچید#کوچ
گایید#گا
گداخت#گداز
گذارد#گذار
گذاشت#گذار
گذراند#گذران
گذشت#گذر
گرازید#گراز
گرانید#گران
گرایید#گرا
گرداند#گردان
گردانید#گردان
گردید#گرد
گرفت#گیر
گروید#گرو
گریاند#گریان
گریخت#گریز
گریزاند#گریزان
گریست#گر
گریست#گری
گزارد#گزار
گزاشت#گزار
گزید#گزین
گسارد#گسار
گستراند#گستران
گسترانید#گستران
گسترد#گستر
گسست#گسل
گسلاند#گسل
گسیخت#گسل
گشاد#گشا
گشت#گرد
گشود#گشا
گفت#گو
گمارد#گمار
گماشت#گمار
گنجاند#گنجان
گنجانید#گنجان
گنجید#گنج
گنداند#گندان
گندید#گند
گوارید#گوار
گوزید#گوز
گیراند#گیران
یازید#یاز
یافت#یاب
یونید#یون
""".strip().split()


# Below code is a modified version of HAZM package's verb conjugator,
# with soem extra verbs (Anything in hazm and not in here? compare needed!)
VERBS_EXC = {}
with_nots = lambda items: items + ["ن" + item for item in items]
simple_ends = ["م", "ی", "", "یم", "ید", "ند"]
narrative_ends = ["ه‌ام", "ه‌ای", "ه", "ه‌ایم", "ه‌اید", "ه‌اند"]
present_ends = ["م", "ی", "د", "یم", "ید", "ند"]

# special case of '#هست':
VERBS_EXC.update({conj: "هست" for conj in ["هست" + end for end in simple_ends]})
VERBS_EXC.update({conj: "هست" for conj in ["نیست" + end for end in simple_ends]})

for verb_root in verb_roots:
    conjugations = []
    if "#" not in verb_root:
        continue
    past, present = verb_root.split("#")

    if past:
        past_simples = [past + end for end in simple_ends]
        past_imperfects = ["می‌" + item for item in past_simples]
        past_narratives = [past + end for end in narrative_ends]
        conjugations = with_nots(past_simples + past_imperfects + past_narratives)
    if present:
        imperatives = ["ب" + present, "ن" + present]
        if present.endswith("ا") or present in ("آ", "گو"):
            present = present + "ی"
        present_simples = [present + end for end in present_ends]
        present_imperfects = ["می‌" + present + end for end in present_ends]
        present_subjunctives = ["ب" + present + end for end in present_ends]
        conjugations += (
            with_nots(present_simples + present_imperfects)
            + present_subjunctives
            + imperatives
        )

    if past.startswith("آ"):
        conjugations = list(
            set(
                map(
                    lambda item: item.replace("بآ", "بیا").replace("نآ", "نیا"),
                    conjugations,
                )
            )
        )

    VERBS_EXC.update({conj: (past,) if past else present for conj in conjugations})
