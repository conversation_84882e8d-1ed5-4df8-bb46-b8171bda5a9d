from ...attrs import LIKE_NUM

_num_words = [
    "nula",
    "jedna",
    "dva",
    "tři",
    "čtyři",
    "pět",
    "šest",
    "sedm",
    "osm",
    "devět",
    "deset",
    "jedenáct",
    "dvan<PERSON>ct",
    "třin<PERSON>ct",
    "čtrnáct",
    "patnáct",
    "šestnáct",
    "sedmnáct",
    "osmnáct",
    "devate<PERSON>ct",
    "dvacet",
    "třicet",
    "čtyřicet",
    "pades<PERSON>t",
    "šedes<PERSON>t",
    "sedmdes<PERSON>t",
    "osmdes<PERSON>t",
    "devades<PERSON>t",
    "sto",
    "tisíc",
    "milion",
    "miliarda",
    "bilion",
    "biliarda",
    "trilion",
    "triliarda",
    "kvadrilion",
    "kvadriliarda",
    "kvintilion",
]


def like_num(text):
    if text.startswith(("+", "-", "±", "~")):
        text = text[1:]
    text = text.replace(",", "").replace(".", "")
    if text.isdigit():
        return True
    if text.count("/") == 1:
        num, denom = text.split("/")
        if num.isdigit() and denom.isdigit():
            return True
    if text.lower() in _num_words:
        return True
    return False


LEX_ATTRS = {LIKE_NUM: like_num}
