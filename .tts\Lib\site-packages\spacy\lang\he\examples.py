"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.he.examples import sentences
>>> docs = nlp.pipe(sentences)
"""


sentences = [
    "סין מקימה קרן של 440 מיליון דולר להשקעה בהייטק בישראל",
    'רה"מ הודיע כי יחרים טקס בחסותו',
    "הכנסת צפויה לאשר איכון אוטומטי של שיחות למוקד 100",
    "תוכנית לאומית תהפוך את ישראל למעצמה דיגיטלית",
    "סע לשלום, המפתחות בפנים.",
    "מלצר, פעמיים טורקי!",
    "ואהבת לרעך כמוך.",
    "היום נעשה משהו בלתי נשכח.",
    "איפה הילד?",
    "מיהו נשיא צרפת?",
    "מהי בירת ארצות הברית?",
    "איך קוראים בעברית לצ'ופצ'יק של הקומקום?",
    "מה הייתה הדקה?",
    "מי אומר שלום ראשון, זה שעולה או זה שיורד?",
]
