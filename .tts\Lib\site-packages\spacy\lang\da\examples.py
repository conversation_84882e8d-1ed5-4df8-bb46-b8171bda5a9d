"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.da.examples import sentences
>>> docs = nlp.pipe(sentences)
"""

sentences = [
    "Apple overvejer at købe et britisk startup for 1 milliard dollar.",
    "Selvkørende biler flytter forsikringsansvaret over på producenterne.",
    "San Francisco overvejer at forbyde udbringningsrobotter på fortovet.",
    "London er en storby i Storbritannien.",
    "Hvor er du?",
    "Hvem er Frankrings president?",
    "Hvad er hovedstaden i USA?",
    "<PERSON>vornår blev <PERSON> født?",
]
