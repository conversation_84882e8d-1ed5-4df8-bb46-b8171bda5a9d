"""
Example sentences to test spaCy and its language models.
>>> from spacy.lang.el.examples import sentences
>>> docs = nlp.pipe(sentences)
"""

sentences = [
    """Η άνιση κατανομή του πλούτου και του εισοδήματος, η οποία έχει λάβει
    τρομερές διαστάσεις, δεν δείχνει τάσεις βελτίωσης.""",
    """Ο στόχος της σύντομης αυτής έκθεσης είναι να συνοψίσει τα κυριότερα
    συμπεράσματα των επισκοπήσεων κάθε μιας χώρας.""",
    """Μέχρι αργά χθες το βράδυ ο πλοιοκτήτης παρέμενε έξω από το γραφείο του
    γενικού γραμματέα του υπουργείου, ενώ είχε μόνον τηλεφωνική επικοινωνία με
    τον υπουργό.""",
    """Σύμφωνα με καλά ενημερωμένη πηγή, από την επεξεργασία του προέκυψε ότι
    οι δράστες της επίθεσης ήταν δύο, καθώς και ότι προσέγγισαν και αποχώρησαν
    από το σημείο με μοτοσικλέτα.""",
    "Η υποδομή καταλυμάτων στην Ελλάδα είναι πλήρης και ανανεώνεται συνεχώς.",
    """Το επείγον ταχυδρομείο (ήτοι το παραδοτέο εντός 48 ωρών το πολύ) μπορεί
    να μεταφέρεται αεροπορικώς μόνον εφόσον εφαρμόζονται οι κανόνες
    ασφαλείας""",
    """'Στις ορεινές περιοχές του νησιού οι χιονοπτώσεις και οι παγετοί είναι
    περιορισμένοι ενώ στις παραθαλάσσιες περιοχές σημειώνονται σπανίως.""",
]
